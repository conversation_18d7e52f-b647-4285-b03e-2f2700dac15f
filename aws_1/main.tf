# プロバイダー設定
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  required_version = ">= 1.0"
}

# AWSプロバイダー設定
provider "aws" {
  region = var.region
}

# VPCの作成
resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name    = "${var.project_name}-vpc"
    Project = var.project_name
  }
}

# Internet Gatewayの作成
resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name    = "${var.project_name}-igw"
    Project = var.project_name
  }
}

# Public Subnetの作成
resource "aws_subnet" "public" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.public_subnet_cidr
  availability_zone       = var.availability_zone
  map_public_ip_on_launch = true

  tags = {
    Name    = "${var.project_name}-public-subnet"
    Project = var.project_name
  }
}

# Route Tableの作成
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name    = "${var.project_name}-public-route-table"
    Project = var.project_name
  }
}

# インターネットへのルート追加
resource "aws_route" "public_internet" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.main.id
}

# Route TableとSubnetの関連付け
resource "aws_route_table_association" "public" {
  subnet_id      = aws_subnet.public.id
  route_table_id = aws_route_table.public.id
}

# Security Groupの作成
resource "aws_security_group" "web" {
  name        = "${var.project_name}-web-sg"
  description = "Security group for web server"
  vpc_id      = aws_vpc.main.id

  # HTTP (80番ポート) を許可
  ingress {
    description = "HTTP from anywhere"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # SSH (22番ポート) を許可
  ingress {
    description = "SSH from allowed IPs"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.ssh_allowed_ips
  }

  # すべてのアウトバウンドトラフィックを許可
  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name    = "${var.project_name}-web-sg"
    Project = var.project_name
  }
}

# 最新のAmazon Linux 2 AMIを取得
data "aws_ami" "amazon_linux_2" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-hvm-*-x86_64-gp2"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# EC2インスタンスの作成
resource "aws_instance" "web" {
  ami           = data.aws_ami.amazon_linux_2.id
  instance_type = var.instance_type
  subnet_id     = aws_subnet.public.id
  
  # セキュリティグループの割り当て
  vpc_security_group_ids = [aws_security_group.web.id]

  # ルートボリューム設定
  root_block_device {
    volume_type = var.root_volume_type
    volume_size = var.root_volume_size
    encrypted   = true
    
    tags = {
      Name    = "${var.project_name}-web-server-root"
      Project = var.project_name
    }
  }

  # ユーザーデータ（簡単なWebサーバーのセットアップ）
  user_data = <<-EOF
    #!/bin/bash
    yum update -y
    yum install -y httpd
    systemctl start httpd
    systemctl enable httpd
    echo "<h1>Hello from AWS EC2!</h1>" > /var/www/html/index.html
    echo "<p>AWS Learning Step 1 - EIP + EC2</p>" >> /var/www/html/index.html
    echo "<p>Instance ID: $(ec2-metadata --instance-id | cut -d' ' -f2)</p>" >> /var/www/html/index.html
    echo "<p>Availability Zone: $(ec2-metadata --availability-zone | cut -d' ' -f2)</p>" >> /var/www/html/index.html
  EOF

  tags = {
    Name    = "${var.project_name}-web-server"
    Project = var.project_name
  }
}

# Elastic IPの作成
resource "aws_eip" "web" {
  domain = "vpc"
  
  tags = {
    Name    = "${var.project_name}-web-server-eip"
    Project = var.project_name
  }
}

# Elastic IPとEC2インスタンスの関連付け
resource "aws_eip_association" "web" {
  instance_id   = aws_instance.web.id
  allocation_id = aws_eip.web.id
}

# 出力情報
output "public_ip" {
  description = "The public IP address of the web server"
  value       = aws_eip.web.public_ip
}

output "public_dns" {
  description = "The public DNS of the web server"
  value       = aws_eip.web.public_dns
}

output "instance_id" {
  description = "The ID of the EC2 instance"
  value       = aws_instance.web.id
}

output "website_url" {
  description = "URL to access the website"
  value       = "http://${aws_eip.web.public_ip}"
}

output "ssh_connection" {
  description = "SSH connection command"
  value       = "ssh -i <your-key.pem> ec2-user@${aws_eip.web.public_ip}"
}