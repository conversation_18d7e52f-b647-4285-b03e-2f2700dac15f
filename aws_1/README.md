# AWS構成① - EIP + EC2

## 概要
このTerraformコードは、AWS学習ステップ大全の構成①（EIP + EC2）を実装したものです。

### 構成内容
- VPC (10.0.0.0/16)
- Internet Gateway
- Public Subnet (********/24)
- Security Group (HTTP:80, SSH:22)
- EC2インスタンス (t2.micro, Amazon Linux 2)
- Elastic IP
- 簡単なWebサーバー（Apache）

## 前提条件
- AWS CLIがインストールされていること
- AWS認証情報が設定されていること
- Terraform 1.0以上がインストールされていること

## 使用方法

### 1. AWS認証情報の設定
```bash
aws configure
```

### 2. ファイルの準備
```bash
# terraform.tfvarsファイルを作成
cp terraform.tfvars.example terraform.tfvars

# 必要に応じてterraform.tfvarsを編集
# 特に、ssh_allowed_ipsを自分のIPアドレスに制限することを推奨
```

### 3. Terraformの初期化
```bash
terraform init
```

### 4. 実行計画の確認
```bash
terraform plan
```

### 5. リソースの作成
```bash
terraform apply
```

### 6. Webサーバーへのアクセス
構築完了後、出力されたURLにアクセスしてWebサーバーが動作していることを確認：
```
http://<Elastic IP>
```

### 7. リソースの削除
```bash
terraform destroy
```

## コスト見積もり（月額）
PDFの資料によると、30日間連続稼働した場合：
- EC2: $10.94（Spotで7割引可能）
- EBS: $0.77（EC2のSSD gp3）
- EIP: $3.60（2024/2に有料化）
- **合計: $15.31**

※停止可能：EC2
※停止不可：EBS, EIP
※無料利用枠あり：EC2, EBS, EIP

## 注意事項
1. **Elastic IP**：EC2が実行中でも停止中でも課金されます（$3.60/月）
2. **セキュリティ**：SSH接続は特定のIPアドレスに制限することを強く推奨します
3. **無料利用枠**：t2.microインスタンスは新規アカウントの場合、12ヶ月間750時間/月まで無料

## セキュリティの改善
本番環境では以下の対策を推奨：
- SSH接続を特定のIPアドレスに制限
- キーペアを使用したSSH認証の実装
- Systems Manager Session Managerの使用を検討

## トラブルシューティング
- EC2インスタンスにSSH接続できない場合は、Security Groupの設定を確認
- Webサーバーにアクセスできない場合は、EC2のステータスチェックを確認
- コンソールからインスタンスのシステムログを確認