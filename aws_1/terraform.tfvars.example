# terraform.tfvars.example
# このファイルをterraform.tfvarsにコピーして、値を設定してください

# AWSリージョン（東京）
region = "ap-northeast-1"

# アベイラビリティゾーン
availability_zone = "ap-northeast-1a"

# VPCのCIDRブロック
vpc_cidr = "10.0.0.0/16"

# パブリックサブネットのCIDRブロック
public_subnet_cidr = "********/24"

# EC2インスタンスタイプ（無料利用枠対象）
instance_type = "t2.micro"

# ルートボリュームのサイズ（GB）
root_volume_size = 8

# ルートボリュームのタイプ
root_volume_type = "gp3"

# プロジェクト名（タグ付けに使用）
project_name = "aws-learning-step-1"

# SSH接続を許可するIPアドレス
# セキュリティのため、自分のIPアドレスのみに制限することを推奨
# 例: ssh_allowed_ips = ["123.456.789.0/32"]
ssh_allowed_ips = ["0.0.0.0/0"]