#!/bin/bash
#
# 設定確認スクリプト
# terraform.tfvarsの設定内容を確認し、作成されるURLを表示します
#

# 色付き出力用の定義
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "========================================="
echo -e "${YELLOW}Terraform設定確認${NC}"
echo "========================================="
echo ""

# terraform.tfvarsが存在するか確認
if [ ! -f "terraform.tfvars" ]; then
    echo -e "${YELLOW}terraform.tfvarsが見つかりません。${NC}"
    echo "terraform.tfvars.exampleをコピーして作成してください："
    echo "  cp terraform.tfvars.example terraform.tfvars"
    exit 1
fi

# 設定値を読み込む（簡易的なパース）
get_tfvar() {
    grep "^$1 " terraform.tfvars | cut -d'"' -f2 | head -1
}

get_tfvar_bool() {
    grep "^$1 " terraform.tfvars | awk '{print $3}' | head -1
}

# 変数の読み込み
DOMAIN=$(get_tfvar "domain_name")
CUSTOM_SUBDOMAIN=$(get_tfvar_bool "create_custom_subdomain")
FULL_SUBDOMAIN=$(get_tfvar "full_subdomain")
CREATE_WWW=$(get_tfvar_bool "create_www_record")
CREATE_WWW_SUB=$(get_tfvar_bool "create_www_for_subdomain")

echo -e "${BLUE}読み込まれた設定:${NC}"
echo "  domain_name: $DOMAIN"
echo "  create_custom_subdomain: $CUSTOM_SUBDOMAIN"
if [ "$CUSTOM_SUBDOMAIN" = "true" ]; then
    echo "  full_subdomain: $FULL_SUBDOMAIN"
    echo "  create_www_for_subdomain: $CREATE_WWW_SUB"
else
    echo "  create_www_record: $CREATE_WWW"
fi
echo ""

echo -e "${GREEN}作成されるDNSレコード:${NC}"
if [ "$CUSTOM_SUBDOMAIN" = "true" ]; then
    # カスタムサブドメインパターン
    echo "  ✓ ${FULL_SUBDOMAIN}.${DOMAIN}"
    if [ "$CREATE_WWW_SUB" = "true" ]; then
        echo "  ✓ www.${FULL_SUBDOMAIN}.${DOMAIN}"
    fi
else
    # 通常のドメインパターン
    echo "  ✓ ${DOMAIN}"
    if [ "$CREATE_WWW" = "true" ]; then
        echo "  ✓ www.${DOMAIN}"
    fi
fi
echo ""

echo -e "${GREEN}アクセス可能なURL:${NC}"
if [ "$CUSTOM_SUBDOMAIN" = "true" ]; then
    echo "  • http://${FULL_SUBDOMAIN}.${DOMAIN}"
    if [ "$CREATE_WWW_SUB" = "true" ]; then
        echo "  • http://www.${FULL_SUBDOMAIN}.${DOMAIN}"
    fi
else
    echo "  • http://${DOMAIN}"
    if [ "$CREATE_WWW" = "true" ]; then
        echo "  • http://www.${DOMAIN}"
    fi
fi
echo ""

echo "========================================="
echo -e "${YELLOW}確認事項:${NC}"
echo "========================================="
echo "1. Route 53にHosted Zoneが存在することを確認："
echo "   aws route53 list-hosted-zones-by-name --dns-name $DOMAIN"
echo ""
echo "2. 既存のレコードと重複しないことを確認："
if [ "$CUSTOM_SUBDOMAIN" = "true" ]; then
    echo "   確認するレコード: ${FULL_SUBDOMAIN}.${DOMAIN}"
else
    echo "   確認するレコード: ${DOMAIN}"
fi
echo ""
echo "3. この設定で問題なければ："
echo "   terraform plan"
echo "   terraform apply"