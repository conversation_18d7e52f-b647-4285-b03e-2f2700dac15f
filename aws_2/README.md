# AWS 構成 ② - Route 53 + EIP + EC2

## 概要

この Terraform コードは、AWS 学習ステップ大全の構成 ②（Route 53 + EIP + EC2）を実装したものです。
構成 ① に独自ドメインでのアクセス機能を追加しています。

### 構成内容

- Route 53（ドメイン管理）
- VPC (10.0.0.0/16)
- Internet Gateway
- Public Subnet (********/24)
- Security Group (HTTP:80, SSH:22)
- EC2 インスタンス (t2.micro, Amazon Linux 2)
- Elastic IP
- 簡単な Web サーバー（Apache）

### 構成 ① からの追加要素

- **Route 53 Hosted Zone**（既存利用 or 新規作成）
- **A レコード**（ドメイン → Elastic IP）
- **www サブドメイン**（オプション）

## 前提条件

- AWS CLI がインストールされていること
- AWS 認証情報が設定されていること
- Terraform 1.0 以上がインストールされていること
- **ドメインを所有していること**（Route 53 で購入済み、または他社で購入し移管済み）

### ⚠️ Route 53 に関する重要な注意事項

- **Route 53** (DNS 管理): グローバルサービス、リージョン指定不要
- **Route 53 Domains** (ドメイン登録): us-east-1 リージョンでのみ利用可能
- ドメイン購入は AWS コンソールの利用を推奨

## 使用方法

### 1. AWS 認証情報の設定

```bash
aws configure
```

### 2. ドメインの準備

#### Option A: Route 53 で新規ドメインを購入

```bash
# Route 53 Domainsはus-east-1リージョンでのみ利用可能
aws route53domains list-domains --region us-east-1

# 注: ドメイン購入はAWSコンソールからの方が簡単です
# Route 53 > Registered domains > Register domain
```

#### Option B: 既存ドメインを Route 53 に移管

1. AWS コンソール > Route 53 > Hosted zones
2. 「Create hosted zone」で Hosted Zone を作成
3. 表示されるネームサーバーをドメインレジストラに設定

### 3. ファイルの準備

```bash
# フォルダ作成と移動
mkdir -p infra/aws_2
cd infra/aws_2

# terraform.tfvarsファイルを作成
cp terraform.tfvars.example terraform.tfvars

# terraform.tfvarsを編集
# 特に以下の項目を設定：
# - domain_name: あなたのドメイン名
# - create_hosted_zone: 新規作成ならtrue、既存利用ならfalse
# - ssh_allowed_ips: 自分のIPアドレスに制限
```

#### パターン 1: カスタムサブドメインを使用する場合

```hcl
# terraform.tfvars の例
domain_name = "hayakai.com"
create_custom_subdomain = true
full_subdomain = "step2.learn"    # → step2.learn.hayakai.com
create_www_for_subdomain = true   # → www.step2.learn.hayakai.com も作成
```

#### パターン 2: 通常のドメインを使用する場合

```hcl
# terraform.tfvars の例
domain_name = "hayakai.com"
create_custom_subdomain = false
create_www_record = true          # → hayakai.com と www.hayakai.com
```

### 4. Terraform の初期化

```bash
terraform init
```

### 5. 実行計画の確認

```bash
terraform plan
```

### 6. リソースの作成

```bash
terraform apply
```

### 7. DNS 設定の確認（新規 Hosted Zone の場合）

```bash
# ネームサーバーの確認
terraform output name_servers

# ドメインレジストラでネームサーバーを設定
# （Route 53以外でドメインを購入した場合）
```

### 8. Web サーバーへのアクセス

DNS 伝播後（最大 48 時間、通常は数分）、以下でアクセス可能：

```
# ドメイン経由
http://example.com
http://www.example.com

# IP経由（すぐにアクセス可能）
http://<Elastic IP>
```

### 9. リソースの削除

```bash
terraform destroy
```

## DNS 伝播の確認方法

```bash
# Aレコードの確認
dig example.com

# ネームサーバーの確認
dig NS example.com

# 特定のネームサーバーに問い合わせ
dig @******* example.com
```

## コスト見積もり（月額）

PDF の資料によると、30 日間連続稼働した場合：

- Route 53: $0.50（Hosted Zone）
- EC2: $10.94（Spot で 7 割引可能）
- EBS: $0.77（EC2 の SSD gp3）
- EIP: $3.60（2024/2 に有料化）
- **合計: $15.81**

※DNS クエリは別途課金（$0.40/100 万クエリ）
※停止可能：EC2
※停止不可：Route 53, EBS, EIP
※無料利用枠あり：EC2, EBS, EIP

## 構成パターン

### パターン 1: カスタムサブドメインを使用

学習用や開発用に、メインドメインを温存しながらサブドメインを使用：

- `step2.learn.hayakai.com` → EC2
- `www.step2.learn.hayakai.com` → EC2（オプション）
- `hayakai.com` は未使用のまま温存

### パターン 2: 通常のドメインを使用

メインドメインを直接使用：

- `hayakai.com` → EC2
- `www.hayakai.com` → EC2

## 構成 ① との違い

| 項目           | 構成 ①                | 構成 ②（通常）     | 構成 ②（カスタム）             |
| -------------- | --------------------- | ------------------ | ------------------------------ |
| アクセス方法   | IP アドレス           | ドメイン名         | サブドメイン                   |
| URL 例         | http://52.xxx.xxx.xxx | http://example.com | http://step2.learn.example.com |
| Route 53       | なし                  | あり               | あり                           |
| 月額コスト     | $15.31                | $15.81             | $15.81                         |
| メインドメイン | -                     | 使用               | 温存                           |

## 注意事項

### セキュリティ

1. **SSH 接続**は特定の IP アドレスに制限することを強く推奨
2. **HTTPS 未対応**：EC2 では ACM の無料証明書が使えないため、構成 ③ 以降で対応

### DNS 関連

1. **TTL（Time To Live）**：300 秒（5 分）に設定
2. **DNS 伝播**：変更が世界中に反映されるまで最大 48 時間
3. **既存 Hosted Zone 利用時**：他のレコードに影響しないよう注意

### コスト最適化

1. **Hosted Zone**：ドメインごとに$0.50/月
2. **複数サブドメイン**：同一 Hosted Zone 内なら追加料金なし
3. **DNS クエリ**：最初の 10 億クエリは$0.40/100 万クエリ

## トラブルシューティング

### ドメインでアクセスできない場合

```bash
# 1. Route 53のレコード確認
aws route53 list-resource-record-sets --hosted-zone-id <ZONE_ID>

# 2. ローカルDNSキャッシュクリア
# Mac/Linux
sudo dscacheutil -flushcache
# Windows
ipconfig /flushdns

# 3. 別のDNSサーバーで確認
nslookup example.com *******
```

### Hosted Zone が見つからない場合

```bash
# Hosted Zone一覧
aws route53 list-hosted-zones

# 特定のドメインで検索
aws route53 list-hosted-zones-by-name --dns-name example.com
```

## 次のステップ

- **構成 ③**：ALB を追加して HTTPS 対応
- **構成 ④**：RDS を追加してデータベース対応
- **構成 ⑤**：CloudFront を追加して CDN 対応
