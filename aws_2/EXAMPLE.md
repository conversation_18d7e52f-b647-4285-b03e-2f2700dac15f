# 構成 ② 設定例集

## 🎯 あなたの状況に合った設定を選んでください

### 例 1: hayakai.com をお持ちで、学習用にサブドメインを使いたい場合

```hcl
# terraform.tfvars
domain_name = "hayakai.com"
create_hosted_zone = false         # 既にHosted Zoneがあるため
create_custom_subdomain = true
full_subdomain = "step2.learn"     # → step2.learn.hayakai.com
create_www_for_subdomain = true    # → www.step2.learn.hayakai.com
```

**作成される URL:**

- http://step2.learn.hayakai.com
- http://www.step2.learn.hayakai.com

### 例 2: 多階層サブドメインを使いたい場合

```hcl
# terraform.tfvars
domain_name = "hayakai.com"
create_hosted_zone = false
create_custom_subdomain = true
full_subdomain = "api.dev"        # → api.dev.hayakai.com
create_www_for_subdomain = false   # www.api.dev.hayakai.com は不要
```

**作成される URL:**

- http://api.dev.hayakai.com

### 例 3: メインドメインを直接使いたい場合

```hcl
# terraform.tfvars
domain_name = "hayakai.com"
create_hosted_zone = false
create_custom_subdomain = false    # メインドメインを使用
create_www_record = true           # www付きも作成
```

**作成される URL:**

- http://hayakai.com
- http://www.hayakai.com

### 例 4: 各構成ごとに異なるサブドメインを使う場合

#### 構成 ② 用

```hcl
full_subdomain = "ec2"            # → ec2.hayakai.com
```

#### 構成 ③ 用（将来）

```hcl
full_subdomain = "alb"            # → alb.hayakai.com
```

#### 構成 ④ 用（将来）

```hcl
full_subdomain = "rds"            # → rds.hayakai.com
```

### 例 5: 環境ごとに分ける場合

```hcl
# 開発環境
full_subdomain = "dev"            # → dev.hayakai.com

# ステージング環境
full_subdomain = "staging"        # → staging.hayakai.com

# 本番環境
full_subdomain = "prod"           # → prod.hayakai.com
```

### 例 6: プロジェクトごとに分ける場合

```hcl
# AWSの学習用
full_subdomain = "aws-learning"   # → aws-learning.hayakai.com

# ポートフォリオ用
full_subdomain = "portfolio"      # → portfolio.hayakai.com

# ブログ用
full_subdomain = "blog"          # → blog.hayakai.com
```

## 💡 推奨設定

### 初めて学習する方

```hcl
create_custom_subdomain = true
full_subdomain = "learn"          # シンプルに
create_www_for_subdomain = false  # www は不要
```

### 転職活動でポートフォリオを作る方

```hcl
create_custom_subdomain = true
full_subdomain = "portfolio"      # 分かりやすく
create_www_for_subdomain = true   # プロフェッショナルに
```

### 実務を想定して練習したい方

```hcl
create_custom_subdomain = true
full_subdomain = "api.dev"        # 実務でよくある構成
create_www_for_subdomain = false
```

## ⚠️ 注意事項

1. **既存のレコードとの重複**

   - 使用予定のサブドメインが既に使われていないか確認

   ```bash
   aws route53 list-resource-record-sets \
     --hosted-zone-id <ZONE_ID> \
     --query "ResourceRecordSets[?Name=='step2.learn.hayakai.com.']"
   ```

2. **ドメイン名の制限**

   - 63 文字以下
   - 英数字とハイフンのみ（ハイフンは先頭・末尾不可）

3. **将来の拡張性**
   - メインドメインは本番サイト用に温存することを推奨
   - サブドメインで実験してから本番へ
