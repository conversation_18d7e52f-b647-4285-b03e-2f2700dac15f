#!/bin/bash
#
# DNS確認スクリプト
# Route 53の設定が正しく反映されているか確認します
#

# 色付き出力用の定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# ドメイン名を引数から取得（なければ入力を求める）
DOMAIN=${1:-}
if [ -z "$DOMAIN" ]; then
    read -p "確認するドメイン名を入力してください: " DOMAIN
fi

# www付きドメインも確認するか
CHECK_WWW=${2:-yes}

echo "========================================="
echo "DNS設定確認: $DOMAIN"
if [ "$CHECK_WWW" = "yes" ]; then
    echo "WWWも確認: www.$DOMAIN"
fi
echo "========================================="
echo ""

# 1. ネームサーバーの確認
echo -e "${YELLOW}1. ネームサーバーの確認${NC}"
echo "----------------------------------------"
NS_RECORDS=$(dig +short NS $DOMAIN)
if [ -z "$NS_RECORDS" ]; then
    echo -e "${RED}✗ ネームサーバーが見つかりません${NC}"
else
    echo -e "${GREEN}✓ ネームサーバー:${NC}"
    echo "$NS_RECORDS"
fi
echo ""

# 2. Aレコードの確認
echo -e "${YELLOW}2. Aレコード（IPアドレス）の確認${NC}"
echo "----------------------------------------"
A_RECORD=$(dig +short A $DOMAIN)
if [ -z "$A_RECORD" ]; then
    echo -e "${RED}✗ Aレコードが見つかりません${NC}"
else
    echo -e "${GREEN}✓ IPアドレス: $A_RECORD${NC}"
fi
echo ""

# 3. 各種DNSサーバーでの確認
echo -e "${YELLOW}3. 主要DNSサーバーでの伝播確認${NC}"
echo "----------------------------------------"
DNS_SERVERS=(
    "*******:Google"
    "*******:Cloudflare"
    "**************:OpenDNS"
)

for server_info in "${DNS_SERVERS[@]}"; do
    IFS=':' read -r server name <<< "$server_info"
    result=$(dig +short @$server $DOMAIN A 2>/dev/null)
    if [ -z "$result" ]; then
        echo -e "${RED}✗ $name ($server): レコードなし${NC}"
    else
        echo -e "${GREEN}✓ $name ($server): $result${NC}"
    fi
done
echo ""

# 4. HTTPアクセス確認
echo -e "${YELLOW}4. HTTPアクセス確認${NC}"
echo "----------------------------------------"
if [ ! -z "$A_RECORD" ]; then
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -m 5 http://$DOMAIN 2>/dev/null)
    if [ "$HTTP_STATUS" = "200" ]; then
        echo -e "${GREEN}✓ HTTP接続成功 (ステータス: $HTTP_STATUS)${NC}"
    elif [ "$HTTP_STATUS" = "000" ]; then
        echo -e "${RED}✗ HTTP接続失敗（タイムアウト）${NC}"
    else
        echo -e "${YELLOW}△ HTTP接続可能 (ステータス: $HTTP_STATUS)${NC}"
    fi
else
    echo -e "${RED}✗ Aレコードがないため確認できません${NC}"
fi
echo ""

# 5. Route 53 Hosted Zone情報（AWS CLI利用可能な場合）
if command -v aws &> /dev/null; then
    echo -e "${YELLOW}5. Route 53 Hosted Zone情報${NC}"
    echo "----------------------------------------"
    ZONE_INFO=$(aws route53 list-hosted-zones-by-name --dns-name $DOMAIN --query 'HostedZones[0]' 2>/dev/null)
    if [ "$ZONE_INFO" != "null" ] && [ ! -z "$ZONE_INFO" ]; then
        echo -e "${GREEN}✓ Hosted Zoneが見つかりました${NC}"
        echo "$ZONE_INFO" | jq -r '.Id' | sed 's|/hostedzone/||'
    else
        echo -e "${YELLOW}△ Hosted Zoneが見つかりません（権限不足の可能性）${NC}"
    fi
else
    echo -e "${YELLOW}5. AWS CLIがインストールされていないため、Route 53情報は確認できません${NC}"
fi
echo ""

# 6. 診断結果
echo "========================================="
echo -e "${YELLOW}診断結果${NC}"
echo "========================================="

if [ ! -z "$A_RECORD" ] && [ "$HTTP_STATUS" = "200" ]; then
    echo -e "${GREEN}✓ すべて正常に動作しています！${NC}"
    echo -e "${GREEN}  http://$DOMAIN でアクセス可能です${NC}"
elif [ ! -z "$A_RECORD" ]; then
    echo -e "${YELLOW}△ DNSは正常ですが、Webサーバーに問題がある可能性があります${NC}"
    echo "  - EC2インスタンスが起動しているか確認してください"
    echo "  - セキュリティグループでポート80が開いているか確認してください"
elif [ ! -z "$NS_RECORDS" ]; then
    echo -e "${YELLOW}△ ネームサーバーは設定されていますが、Aレコードがありません${NC}"
    echo "  - Terraformが正常に実行されたか確認してください"
    echo "  - Route 53でAレコードが作成されているか確認してください"
else
    echo -e "${RED}✗ DNS設定に問題があります${NC}"
    echo "  - ドメインのネームサーバーがRoute 53に向いているか確認してください"
    echo "  - DNS伝播に最大48時間かかる場合があります"
fi