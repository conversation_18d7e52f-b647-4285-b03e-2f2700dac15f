# 変数定義ファイル

variable "region" {
  description = "AWS region"
  type        = string
  default     = "ap-northeast-1"
}

variable "availability_zone" {
  description = "Availability zone"
  type        = string
  default     = "ap-northeast-1a"
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_cidr" {
  description = "CIDR block for public subnet"
  type        = string
  default     = "********/24"
}

variable "instance_type" {
  description = "EC2 instance type"
  type        = string
  default     = "t2.micro"
}

variable "root_volume_size" {
  description = "Size of the root volume in GB"
  type        = number
  default     = 8
}

variable "root_volume_type" {
  description = "Type of the root volume"
  type        = string
  default     = "gp3"
}

variable "project_name" {
  description = "Project name for tagging"
  type        = string
  default     = "aws-learning-step-2"
}

variable "ssh_allowed_ips" {
  description = "List of IPs allowed to SSH"
  type        = list(string)
  default     = ["0.0.0.0/0"]  # セキュリティのため、実際は特定のIPに制限することを推奨
}

# Route 53関連の変数
variable "domain_name" {
  description = "The domain name to use for the website"
  type        = string
  # 例: "example.com"
}

variable "create_hosted_zone" {
  description = "Whether to create a new hosted zone or use an existing one"
  type        = bool
  default     = false  # 既存のHosted Zoneを使用する場合はfalse
}

variable "create_www_record" {
  description = "Whether to create a www subdomain record"
  type        = bool
  default     = true
}

# カスタムサブドメイン関連の変数
variable "create_custom_subdomain" {
  description = "Whether to create a custom subdomain instead of using the root domain"
  type        = bool
  default     = false
}

variable "full_subdomain" {
  description = "Full subdomain to use (e.g., 'api.dev' for api.dev.example.com)"
  type        = string
  default     = ""
}

variable "create_www_for_subdomain" {
  description = "Whether to create www prefix for the custom subdomain"
  type        = bool
  default     = false
}