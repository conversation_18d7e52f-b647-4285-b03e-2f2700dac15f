# プロバイダー設定
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  required_version = ">= 1.0"
}

# AWSプロバイダー設定
provider "aws" {
  region = var.region
}

# VPCの作成
resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name    = "${var.project_name}-vpc"
    Project = var.project_name
  }
}

# Internet Gatewayの作成
resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name    = "${var.project_name}-igw"
    Project = var.project_name
  }
}

# Public Subnetの作成
resource "aws_subnet" "public" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.public_subnet_cidr
  availability_zone       = var.availability_zone
  map_public_ip_on_launch = true

  tags = {
    Name    = "${var.project_name}-public-subnet"
    Project = var.project_name
  }
}

# Route Tableの作成
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name    = "${var.project_name}-public-route-table"
    Project = var.project_name
  }
}

# インターネットへのルート追加
resource "aws_route" "public_internet" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.main.id
}

# Route TableとSubnetの関連付け
resource "aws_route_table_association" "public" {
  subnet_id      = aws_subnet.public.id
  route_table_id = aws_route_table.public.id
}

# Security Groupの作成
resource "aws_security_group" "web" {
  name        = "${var.project_name}-web-sg"
  description = "Security group for web server"
  vpc_id      = aws_vpc.main.id

  # HTTP (80番ポート) を許可
  ingress {
    description = "HTTP from anywhere"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # SSH (22番ポート) を許可
  ingress {
    description = "SSH from allowed IPs"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.ssh_allowed_ips
  }

  # すべてのアウトバウンドトラフィックを許可
  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name    = "${var.project_name}-web-sg"
    Project = var.project_name
  }
}

# 最新のAmazon Linux 2 AMIを取得
data "aws_ami" "amazon_linux_2" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-hvm-*-x86_64-gp2"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# EC2インスタンスの作成
resource "aws_instance" "web" {
  ami           = data.aws_ami.amazon_linux_2.id
  instance_type = var.instance_type
  subnet_id     = aws_subnet.public.id
  
  # セキュリティグループの割り当て
  vpc_security_group_ids = [aws_security_group.web.id]

  # ルートボリューム設定
  root_block_device {
    volume_type = var.root_volume_type
    volume_size = var.root_volume_size
    encrypted   = true
    
    tags = {
      Name    = "${var.project_name}-web-server-root"
      Project = var.project_name
    }
  }

  # ユーザーデータ（簡単なWebサーバーのセットアップ）
  user_data = <<-EOF
    #!/bin/bash
    yum update -y
    yum install -y httpd
    systemctl start httpd
    systemctl enable httpd
    
    # 使用するドメイン名を決定
    DISPLAY_DOMAIN="${var.create_custom_subdomain ? "${var.full_subdomain}.${var.domain_name}" : var.domain_name}"
    
    # ドメイン名を含むHTMLページの作成
    cat > /var/www/html/index.html <<HTML
    <!DOCTYPE html>
    <html lang="ja">
    <head>
        <meta charset="UTF-8">
        <title>AWS Learning Step 2</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 50px; }
            .info { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
            .domain { color: #0066cc; font-weight: bold; }
        </style>
    </head>
    <body>
        <h1>Hello from AWS EC2 with Route 53!</h1>
        <div class="info">
            <p>AWS Learning Step 2 - Route 53 + EIP + EC2</p>
            <p>Domain: <span class="domain">$DISPLAY_DOMAIN</span></p>
            <p>Instance ID: <span id="instance-id">Loading...</span></p>
            <p>Availability Zone: <span id="az">Loading...</span></p>
            <p>Public IP: <span id="public-ip">Loading...</span></p>
        </div>
        <script>
            // メタデータの取得
            fetch('http://***************/latest/meta-data/instance-id')
                .then(response => response.text())
                .then(data => document.getElementById('instance-id').textContent = data)
                .catch(() => document.getElementById('instance-id').textContent = 'Unable to fetch');
                
            fetch('http://***************/latest/meta-data/placement/availability-zone')
                .then(response => response.text())
                .then(data => document.getElementById('az').textContent = data)
                .catch(() => document.getElementById('az').textContent = 'Unable to fetch');
                
            fetch('http://***************/latest/meta-data/public-ipv4')
                .then(response => response.text())
                .then(data => document.getElementById('public-ip').textContent = data)
                .catch(() => document.getElementById('public-ip').textContent = 'Unable to fetch');
        </script>
    </body>
    </html>
HTML
  EOF

  tags = {
    Name    = "${var.project_name}-web-server"
    Project = var.project_name
  }
}

# Elastic IPの作成
resource "aws_eip" "web" {
  domain = "vpc"
  
  tags = {
    Name    = "${var.project_name}-web-server-eip"
    Project = var.project_name
  }
}

# Elastic IPとEC2インスタンスの関連付け
resource "aws_eip_association" "web" {
  instance_id   = aws_instance.web.id
  allocation_id = aws_eip.web.id
}

# Route 53 Hosted Zoneの作成（既存のドメインを使用する場合はdata sourceを使用）
# 新規ドメインの場合
resource "aws_route53_zone" "main" {
  count = var.create_hosted_zone ? 1 : 0
  name  = var.domain_name

  tags = {
    Name    = "${var.project_name}-hosted-zone"
    Project = var.project_name
  }
}

# 既存のHosted Zoneを使用する場合
data "aws_route53_zone" "existing" {
  count = var.create_hosted_zone ? 0 : 1
  name  = var.domain_name
}

# 使用するHosted Zone IDを決定
locals {
  zone_id = var.create_hosted_zone ? aws_route53_zone.main[0].zone_id : data.aws_route53_zone.existing[0].zone_id
}

# Route 53 Aレコードの作成（ルートドメイン）
resource "aws_route53_record" "root" {
  count   = !var.create_custom_subdomain ? 1 : 0
  zone_id = local.zone_id
  name    = var.domain_name
  type    = "A"
  ttl     = 300
  records = [aws_eip.web.public_ip]
}

# Route 53 Aレコードの作成（www）
resource "aws_route53_record" "www" {
  count   = !var.create_custom_subdomain && var.create_www_record ? 1 : 0
  zone_id = local.zone_id
  name    = "www.${var.domain_name}"
  type    = "A"
  ttl     = 300
  records = [aws_eip.web.public_ip]
}

# カスタムサブドメインのAレコード（例: step2.learn.hayakai.com）
resource "aws_route53_record" "custom_subdomain" {
  count   = var.create_custom_subdomain ? 1 : 0
  zone_id = local.zone_id
  name    = "${var.full_subdomain}.${var.domain_name}"
  type    = "A"
  ttl     = 300
  records = [aws_eip.web.public_ip]
}

# カスタムサブドメインのwww版（例: www.step2.learn.hayakai.com）
resource "aws_route53_record" "www_custom_subdomain" {
  count   = var.create_custom_subdomain && var.create_www_for_subdomain ? 1 : 0
  zone_id = local.zone_id
  name    = "www.${var.full_subdomain}.${var.domain_name}"
  type    = "A"
  ttl     = 300
  records = [aws_eip.web.public_ip]
}

# 出力情報
output "public_ip" {
  description = "The public IP address of the web server"
  value       = aws_eip.web.public_ip
}

output "public_dns" {
  description = "The public DNS of the web server"
  value       = aws_eip.web.public_dns
}

output "instance_id" {
  description = "The ID of the EC2 instance"
  value       = aws_instance.web.id
}

output "primary_url" {
  description = "Primary URL to access the website"
  value       = var.create_custom_subdomain ? "http://${var.full_subdomain}.${var.domain_name}" : "http://${var.domain_name}"
}

output "www_url" {
  description = "WWW URL to access the website"
  value       = var.create_custom_subdomain && var.create_www_for_subdomain ? "http://www.${var.full_subdomain}.${var.domain_name}" : (!var.create_custom_subdomain && var.create_www_record ? "http://www.${var.domain_name}" : "Not created")
}

output "website_ip_url" {
  description = "URL to access the website via IP"
  value       = "http://${aws_eip.web.public_ip}"
}

output "name_servers" {
  description = "Name servers for the hosted zone (if created)"
  value       = var.create_hosted_zone ? aws_route53_zone.main[0].name_servers : ["Using existing hosted zone"]
}

output "ssh_connection" {
  description = "SSH connection command"
  value       = "ssh -i <your-key.pem> ec2-user@${aws_eip.web.public_ip}"
}

output "dns_records_created" {
  description = "List of DNS records created"
  value = compact(var.create_custom_subdomain ? [
    "${var.full_subdomain}.${var.domain_name}",
    var.create_www_for_subdomain ? "www.${var.full_subdomain}.${var.domain_name}" : ""
  ] : [
    var.domain_name,
    var.create_www_record ? "www.${var.domain_name}" : ""
  ])
}