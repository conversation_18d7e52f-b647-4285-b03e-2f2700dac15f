# Route 53 セットアップガイド

## 🎯 このガイドの目的

構成 ② を構築する前に、Route 53 でドメインを準備する方法を説明します。

## 📋 前提条件チェックリスト

- [ ] AWS アカウントを持っている
- [ ] ドメインを持っている、または購入予定
- [ ] AWS CLI がセットアップ済み

## 🌐 ドメイン準備の 3 つのパターン

### パターン 1: Route 53 で新規ドメインを購入

1. **AWS コンソールでドメイン購入**

   ```
   Route 53 > Domains > Register domain
   ```

   - ドメイン名を検索
   - 利用可能なら「Add to cart」
   - 連絡先情報を入力（Privacy Protection 推奨）
   - 購入完了まで最大 3 日

2. **自動的に Hosted Zone が作成される**

   ```bash
   # Hosted Zoneの確認（Route 53はグローバルサービス）
   aws route53 list-hosted-zones

   # ドメイン一覧の確認（us-east-1でのみ実行可能）
   aws route53domains list-domains --region us-east-1
   ```

3. **terraform.tfvars の設定**
   ```hcl
   domain_name = "your-domain.com"
   create_hosted_zone = false  # 既に作成済みなのでfalse
   ```

### パターン 2: 他社で購入済みドメインを Route 53 で管理

1. **Route 53 で Hosted Zone を作成**

   ```bash
   # Terraformで作成する場合
   create_hosted_zone = true

   # または手動で作成
   aws route53 create-hosted-zone \
     --name example.com \
     --caller-reference $(date +%s)
   ```

2. **ネームサーバーを確認**

   ```bash
   # Terraform実行後
   terraform output name_servers

   # または直接確認
   aws route53 get-hosted-zone --id <ZONE_ID>
   ```

3. **ドメインレジストラでネームサーバーを変更**
   - お名前.com、ムームードメインなどの管理画面
   - DNS サーバー設定で、Route 53 のネームサーバー 4 つを設定
   ```
   ns-123.awsdns-12.com
   ns-456.awsdns-34.net
   ns-789.awsdns-56.org
   ns-012.awsdns-78.co.uk
   ```

### パターン 3: 既存の Route 53 Hosted Zone を使用

1. **既存の Hosted Zone ID を確認**

   ```bash
   aws route53 list-hosted-zones-by-name \
     --dns-name example.com
   ```

2. **terraform.tfvars の設定**
   ```hcl
   domain_name = "example.com"
   create_hosted_zone = false
   ```

## 🔍 動作確認方法

### 1. Hosted Zone の確認

```bash
# Hosted Zone一覧
aws route53 list-hosted-zones

# 特定ドメインの詳細
aws route53 list-resource-record-sets \
  --hosted-zone-id <ZONE_ID>
```

### 2. DNS 伝播の確認

```bash
# ネームサーバーの確認
dig NS example.com

# Aレコードの確認（Terraform実行後）
dig A example.com

# 世界各地からのDNS確認
# https://www.whatsmydns.net/
```

## ⚠️ よくある問題と解決方法

### 問題 1: Hosted Zone が見つからない

```bash
# エラー例
Error: no matching Route53Zone found

# 解決方法
# 1. ドメイン名の確認（末尾のドットに注意）
domain_name = "example.com"  # OK
domain_name = "example.com." # NG

# 2. Hosted Zoneの存在確認
aws route53 list-hosted-zones-by-name
```

### 問題 2: DNS 伝播が遅い

```bash
# 確認方法
# 1. Route 53のネームサーバーに直接問い合わせ
dig @ns-123.awsdns-12.com example.com

# 2. パブリックDNSで確認
dig @******* example.com
dig @******* example.com

# 3. ローカルキャッシュをクリア
sudo dscacheutil -flushcache  # Mac
ipconfig /flushdns            # Windows
```

### 問題 3: 既存レコードとの競合

```bash
# 既存レコードの確認
aws route53 list-resource-record-sets \
  --hosted-zone-id <ZONE_ID> \
  --query "ResourceRecordSets[?Name=='example.com.']"

# 必要なら既存レコードを削除してからTerraform実行
```

## 💰 コスト計算

| 項目         | 料金         | 備考               |
| ------------ | ------------ | ------------------ |
| Hosted Zone  | $0.50/月     | ドメインごと       |
| DNS クエリ   | $0.40/100 万 | 最初の 10 億クエリ |
| ドメイン登録 | $12〜/年     | .com の場合        |
| ドメイン移管 | 無料         | 更新料は必要       |

## 📝 チェックリスト

構成 ② を構築する前に：

- [ ] ドメインを所有している
- [ ] Route 53 Hosted Zone が存在する
- [ ] ネームサーバーが正しく設定されている
- [ ] `domain_name`変数を設定した
- [ ] `create_hosted_zone`を適切に設定した

## 🚀 準備完了！

上記の準備が整ったら、README.md の手順に従って Terraform を実行してください。
