# terraform.tfvars.example
# このファイルをterraform.tfvarsにコピーして、値を設定してください

# AWSリージョン（東京）
region = "ap-northeast-1"

# アベイラビリティゾーン
availability_zone = "ap-northeast-1a"

# VPCのCIDRブロック
vpc_cidr = "10.0.0.0/16"

# パブリックサブネットのCIDRブロック
public_subnet_cidr = "********/24"

# EC2インスタンスタイプ（無料利用枠対象）
instance_type = "t2.micro"

# ルートボリュームのサイズ（GB）
root_volume_size = 8

# ルートボリュームのタイプ
root_volume_type = "gp3"

# プロジェクト名（タグ付けに使用）
project_name = "aws-learning-step-2"

# SSH接続を許可するIPアドレス
# セキュリティのため、自分のIPアドレスのみに制限することを推奨
# 例: ssh_allowed_ips = ["123.456.789.0/32"]
ssh_allowed_ips = ["0.0.0.0/0"]

#===========================================
# Route 53関連の設定
#===========================================

# 使用するドメイン名（必須）
# Route 53で管理されているドメインを指定してください
domain_name = "example.com"

# 新規Hosted Zoneを作成するか
# - true: 新しいHosted Zoneを作成（新規ドメインの場合）
# - false: 既存のHosted Zoneを使用（既にRoute 53で管理している場合）
create_hosted_zone = false

#===========================================
# パターン1: カスタムサブドメインを使用
#===========================================
# 例: step2.learn.example.com を作成したい場合
# create_custom_subdomain = true
# full_subdomain = "step2.learn"
# create_www_for_subdomain = true  # www.step2.learn.example.com も作成

#===========================================
# パターン2: 通常のドメインを使用（デフォルト）
#===========================================
# 例: example.com と www.example.com を作成したい場合
# create_custom_subdomain = false
# create_www_record = true

# デフォルト設定（パターン2）
create_custom_subdomain = false
create_www_record = true

# カスタムサブドメイン設定（パターン1を使う場合はコメントアウトを外す）
# create_custom_subdomain = true
# full_subdomain = "step2.learn"
# create_www_for_subdomain = true