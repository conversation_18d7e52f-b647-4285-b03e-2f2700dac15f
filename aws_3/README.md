# AWS Learning Step 3 - VPC with Public/Private Subnets and NAT Gateway

このTerraformスクリプトは、AWS Learning Step 3のパターンを実装します。
パブリックサブネットとプライベートサブネットを持つVPCを作成し、NAT Gatewayの有無を選択できます。

## 構成要素

- VPC
- Internet Gateway
- Public Subnet (パブリックサブネット)
- Private Subnet (プライベートサブネット)
- NAT Gateway (オプション)
- Route Tables (パブリック用・プライベート用)
- Security Groups
- EC2インスタンス (パブリック・プライベート)

## 使用方法

1. `terraform.tfvars`ファイルを作成し、必要な変数を設定
2. `terraform init`でTerraformを初期化
3. `terraform plan`で実行計画を確認
4. `terraform apply`でリソースを作成

## NAT Gatewayについて

`enable_nat_gateway`変数でNAT Gatewayの作成を制御できます：
- `true`: NAT Gatewayを作成し、プライベートサブネットからインターネットアクセス可能
- `false`: NAT Gatewayを作成せず、プライベートサブネットはインターネットアクセス不可

## 注意事項

- NAT Gatewayは有料サービスです
- プライベートサブネットのEC2インスタンスにSSH接続するには、パブリックサブネットのインスタンス経由（踏み台サーバー）が必要です

## 変数

主要な変数については`variables.tf`を参照してください。

## 出力

- パブリックインスタンスのパブリックIP
- プライベートインスタンスのプライベートIP
- SSH接続コマンド
- NAT Gatewayの情報（作成時のみ）
