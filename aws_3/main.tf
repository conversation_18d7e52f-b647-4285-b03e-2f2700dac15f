# プロバイダー設定
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  required_version = ">= 1.0"
}

# AWSプロバイダー設定
provider "aws" {
  region = var.region
}

# VPCの作成
resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = merge(
    {
      Name    = "${var.project_name}-vpc"
      Project = var.project_name
    },
    var.additional_tags
  )
}

# Internet Gatewayの作成
resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    {
      Name    = "${var.project_name}-igw"
      Project = var.project_name
    },
    var.additional_tags
  )
}

# Public Subnetの作成
resource "aws_subnet" "public" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.public_subnet_cidr
  availability_zone       = var.availability_zone
  map_public_ip_on_launch = true

  tags = merge(
    {
      Name    = "${var.project_name}-public-subnet"
      Project = var.project_name
      Type    = "Public"
    },
    var.additional_tags
  )
}

# Private Subnetの作成
resource "aws_subnet" "private" {
  vpc_id            = aws_vpc.main.id
  cidr_block        = var.private_subnet_cidr
  availability_zone = var.availability_zone

  tags = merge(
    {
      Name    = "${var.project_name}-private-subnet"
      Project = var.project_name
      Type    = "Private"
    },
    var.additional_tags
  )
}

# Elastic IP for NAT Gateway (NAT Gatewayが有効な場合のみ)
resource "aws_eip" "nat" {
  count  = var.enable_nat_gateway ? 1 : 0
  domain = "vpc"

  tags = merge(
    {
      Name    = "${var.project_name}-nat-eip"
      Project = var.project_name
    },
    var.additional_tags
  )

  depends_on = [aws_internet_gateway.main]
}

# NAT Gatewayの作成 (有効な場合のみ)
resource "aws_nat_gateway" "main" {
  count         = var.enable_nat_gateway ? 1 : 0
  allocation_id = aws_eip.nat[0].id
  subnet_id     = aws_subnet.public.id

  tags = merge(
    {
      Name    = "${var.project_name}-nat-gateway"
      Project = var.project_name
    },
    var.additional_tags
  )

  depends_on = [aws_internet_gateway.main]
}

# Public Route Tableの作成
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    {
      Name    = "${var.project_name}-public-route-table"
      Project = var.project_name
      Type    = "Public"
    },
    var.additional_tags
  )
}

# Private Route Tableの作成
resource "aws_route_table" "private" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    {
      Name    = "${var.project_name}-private-route-table"
      Project = var.project_name
      Type    = "Private"
    },
    var.additional_tags
  )
}

# Public Route Table - インターネットへのルート
resource "aws_route" "public_internet" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.main.id
}

# Private Route Table - NAT Gatewayへのルート (NAT Gatewayが有効な場合のみ)
resource "aws_route" "private_nat" {
  count                  = var.enable_nat_gateway ? 1 : 0
  route_table_id         = aws_route_table.private.id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.main[0].id
}

# Route TableとSubnetの関連付け - Public
resource "aws_route_table_association" "public" {
  subnet_id      = aws_subnet.public.id
  route_table_id = aws_route_table.public.id
}

# Route TableとSubnetの関連付け - Private
resource "aws_route_table_association" "private" {
  subnet_id      = aws_subnet.private.id
  route_table_id = aws_route_table.private.id
}
